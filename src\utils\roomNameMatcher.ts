import stringSimilarity from 'string-similarity';

/**
 * Utility functions for matching room names between hotel database and PDF extraction
 * Handles variations in room names like missing words, different spellings, etc.
 */

/**
 * Normalize room name for comparison
 * - Convert to lowercase
 * - Remove extra spaces
 * - Remove common words that might vary
 * - Remove special characters
 */
export const normalizeRoomName = (name: string): string => {
  if (!name) return '';
  
  return name
    .toLowerCase()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[^\w\s]/g, '') // Remove special characters
    .replace(/\b(room|type|category|suite|deluxe|standard|premium|luxury)\b/g, '') // Remove common room type words
    .trim();
};

/**
 * Extract key words from room name for keyword matching
 */
export const extractKeywords = (name: string): string[] => {
  const normalized = normalizeRoomName(name);
  return normalized.split(' ').filter(word => word.length > 2); // Filter out short words
};

/**
 * Calculate similarity score between two room names using multiple methods
 */
export const calculateRoomNameSimilarity = (name1: string, name2: string): number => {
  if (!name1 || !name2) return 0;
  
  // Method 1: Direct string similarity
  const directSimilarity = stringSimilarity.compareTwoStrings(
    normalizeRoomName(name1),
    normalizeRoomName(name2)
  );
  
  // Method 2: Keyword matching
  const keywords1 = extractKeywords(name1);
  const keywords2 = extractKeywords(name2);
  
  let keywordMatches = 0;
  const totalKeywords = Math.max(keywords1.length, keywords2.length);
  
  if (totalKeywords > 0) {
    keywords1.forEach(keyword1 => {
      const bestMatch = stringSimilarity.findBestMatch(keyword1, keywords2);
      if (bestMatch.bestMatch.rating > 0.7) {
        keywordMatches++;
      }
    });
    
    keywords2.forEach(keyword2 => {
      const bestMatch = stringSimilarity.findBestMatch(keyword2, keywords1);
      if (bestMatch.bestMatch.rating > 0.7) {
        keywordMatches++;
      }
    });
    
    // Avoid double counting
    keywordMatches = Math.min(keywordMatches, totalKeywords);
  }
  
  const keywordSimilarity = totalKeywords > 0 ? keywordMatches / totalKeywords : 0;
  
  // Method 3: Substring matching
  const norm1 = normalizeRoomName(name1);
  const norm2 = normalizeRoomName(name2);
  const substringMatch = (norm1.includes(norm2) || norm2.includes(norm1)) ? 0.8 : 0;
  
  // Combine all methods with weights
  const finalScore = (directSimilarity * 0.4) + (keywordSimilarity * 0.4) + (substringMatch * 0.2);
  
  return Math.min(finalScore, 1.0); // Ensure score doesn't exceed 1.0
};

/**
 * Find the best matching room name from a list of candidates
 */
export const findBestRoomMatch = (
  targetRoomName: string,
  candidateRooms: Array<{ hotelRoomId: string; hotelRoomType: string }>,
  threshold: number = 0.6
): { room: { hotelRoomId: string; hotelRoomType: string } | null; score: number } => {
  if (!targetRoomName || !candidateRooms || candidateRooms.length === 0) {
    return { room: null, score: 0 };
  }
  
  let bestMatch = { room: null as any, score: 0 };
  
  candidateRooms.forEach(room => {
    const score = calculateRoomNameSimilarity(targetRoomName, room.hotelRoomType);
    if (score > bestMatch.score && score >= threshold) {
      bestMatch = { room, score };
    }
  });
  
  return bestMatch;
};

/**
 * Enhanced room filtering function that uses fuzzy matching
 */
export const filterRoomsByName = (
  extractedRoomName: string,
  selectedRoomName: string,
  threshold: number = 0.6
): boolean => {
  if (!extractedRoomName || !selectedRoomName) return true; // Include if no room category specified
  
  const similarity = calculateRoomNameSimilarity(extractedRoomName, selectedRoomName);
  
  // Also check the original simple matching as fallback
  const normalizedExtracted = normalizeRoomName(extractedRoomName);
  const normalizedSelected = normalizeRoomName(selectedRoomName);
  
  const simpleMatch = normalizedExtracted === normalizedSelected ||
                     normalizedExtracted.includes(normalizedSelected) ||
                     normalizedSelected.includes(normalizedExtracted);
  
  return similarity >= threshold || simpleMatch;
};
